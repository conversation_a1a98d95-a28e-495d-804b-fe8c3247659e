"""
异步API优化方案
修复异步模式中的关键问题
"""
import asyncio
import time
from typing import List, Dict, Any, Optional, Tuple
import threading
import math

class AsyncTokenBucketRateLimiter:
    """
    异步令牌桶速率限制器
    用于控制全局的Token发送速率，以符合API的分钟配额。
    """
    def __init__(self, capacity: int, refill_rate: float):
        """
        初始化
        :param capacity: 桶的容量 (例如, 250000 tokens)
        :param refill_rate: 每秒补充的令牌数 (例如, 250000 / 60)
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self._tokens = capacity
        self._last_refill_time = time.monotonic()
        self._lock = asyncio.Lock()

    def _refill(self):
        """补充令牌"""
        now = time.monotonic()
        time_passed = now - self._last_refill_time
        tokens_to_add = time_passed * self.refill_rate
        self._tokens = min(self.capacity, self._tokens + tokens_to_add)
        self._last_refill_time = now

    async def acquire(self, tokens_needed: int):
        """获取指定数量的令牌，如果不足则异步等待"""
        async with self._lock:
            self._refill()
            while self._tokens < tokens_needed:
                await asyncio.sleep(0.1)  # 等待0.1秒后重试
                self._refill()
            self._tokens -= tokens_needed

class OptimizedAsyncGeminiAPIManager:
    """优化的异步Gemini API管理器"""
    
    def __init__(self, api_keys: List[str], model_names: List[str]):
        # 过滤有效的API密钥
        self.api_configs = []
        for i, key in enumerate(api_keys):
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0,
                    "semaphore": asyncio.Semaphore(1),  # 每个API密钥最多1个并发
                    "error_count": 0,  # 错误计数
                    "last_error_time": 0,  # 最后错误时间
                    "is_available": True  # 可用状态
                })
        
        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")
        
        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        
        # 使用线程锁而不是异步锁，避免死锁
        self.index_lock = threading.Lock()
        
        # 统计信息
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}
        self.success_counts = {i: 0 for i in range(self.total_keys)}
        
        # 新增：全局速率限制器，基于Google Free Tier的 250,000 input tokens per minute
        # 速率为 250000 / 60 ≈ 4166 tokens/sec
        self.rate_limiter = AsyncTokenBucketRateLimiter(capacity=250000, refill_rate=4166)
        print(f"Global rate limiter initialized: capacity=250000, refill_rate=4166 tokens/sec")

        print(f"Optimized Async Gemini API Manager initialized with {self.total_keys} active keys.")
        print(f"Each key supports max 1 concurrent request, total max concurrent: {self.total_keys}")
    
    def _get_next_available_api(self) -> Optional[Dict[str, Any]]:
        """获取下一个可用的API配置（改进版）"""
        with self.index_lock:
            attempts = 0
            start_index = self.current_api_index
            
            while attempts < self.total_keys:
                api_config = self.api_configs[self.current_api_index]
                
                # 检查API是否可用
                if (api_config["is_available"] and 
                    not api_config["semaphore"].locked() and
                    api_config["error_count"] < 5):  # 错误次数限制
                    
                    # 找到可用的API
                    model_name = api_config["models"][api_config["current_model_index"]]
                    result = {
                        "api_index": self.current_api_index,
                        "api_name": api_config["name"],
                        "api_key": api_config["key"],
                        "model_name": model_name,
                        "semaphore": api_config["semaphore"]
                    }
                    
                    # 移动到下一个API（负载均衡）
                    self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    return result
                
                # 尝试下一个API
                self.current_api_index = (self.current_api_index + 1) % self.total_keys
                attempts += 1
            
            # 如果所有API都不可用，重置错误状态
            self._reset_error_states()
            return None
    
    def _reset_error_states(self):
        """重置API错误状态"""
        current_time = time.time()
        for config in self.api_configs:
            # 如果距离上次错误超过5分钟，重置状态
            if current_time - config["last_error_time"] > 300:
                config["error_count"] = 0
                config["is_available"] = True
    
    def _mark_api_error(self, api_index: int, error_msg: str):
        """标记API错误"""
        with self.index_lock:
            if api_index < len(self.api_configs):
                config = self.api_configs[api_index]
                config["error_count"] += 1
                config["last_error_time"] = time.time()
                
                # 如果错误次数过多，暂时禁用
                if config["error_count"] >= 5:
                    config["is_available"] = False
                    print(f"⚠️ API {config['name']} 暂时禁用（错误次数过多）")
    
    def _estimate_tokens(self, text: str) -> int:
        """
        估算文本的Token数量，用于速率限制。
        这是一个简化的估算，实际Token数可能因模型而异。
        """
        # 简单估算：1个字符约等于1.5个token（对中英文混合文本的粗略估计）
        return math.ceil(len(text) * 1.5)

    async def generate_content_with_model_async(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """优化的异步内容生成"""
        max_retries = 3
        retry_delay = 1
        
        for retry in range(max_retries):
            # 获取可用的API配置
            api_config = None
            wait_attempts = 0
            max_wait_attempts = 10
            
            while api_config is None and wait_attempts < max_wait_attempts:
                api_config = self._get_next_available_api()
                if api_config is None:
                    # 等待一段时间后重试
                    await asyncio.sleep(0.5)
                    wait_attempts += 1
            
            if api_config is None:
                if retry < max_retries - 1:
                    print(f"⚠️ 所有API暂时不可用，等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise Exception("所有API密钥都不可用，无法继续")
            
            api_index = api_config["api_index"]
            semaphore = api_config["semaphore"]
            
            # 新增：在调用API前，先从全局速率限制器获取令牌
            estimated_tokens = self._estimate_tokens(prompt)
            if estimated_tokens > self.rate_limiter.capacity:
                raise ValueError(f"Prompt is too large ({estimated_tokens} tokens) for the rate limiter capacity ({self.rate_limiter.capacity}). Please use the chunking mechanism.")
            print(f"Rate Limiter: Acquiring {estimated_tokens} tokens...")
            await self.rate_limiter.acquire(estimated_tokens)
            print(f"Rate Limiter: Tokens acquired.")

            # 使用信号量控制并发
            async with semaphore:
                try:
                    # 更新调用计数
                    with self.index_lock:
                        self.api_call_counts[api_index] += 1
                    
                    print(f"[异步API调用] 使用: {api_config['api_name']} | 模型: {model_name} | 调用数: {self.api_call_counts[api_index]}")
                    
                    # 配置API
                    import google.generativeai as genai
                    genai.configure(api_key=api_config["api_key"])
                    
                    # 生成配置
                    generation_config = genai.types.GenerationConfig(
                        temperature=0.0,
                        max_output_tokens=8192,  # 适中的token限制
                        top_p=0.95,
                        top_k=40
                    )
                    
                    # 创建模型并生成内容
                    model = genai.GenerativeModel(model_name, generation_config=generation_config)
                    response = await asyncio.to_thread(model.generate_content, [prompt])
                    
                    # 记录成功
                    with self.index_lock:
                        self.success_counts[api_index] += 1
                    
                    return response, api_index
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    print(f"❌ API调用失败: {api_config['api_name']} - {error_msg}")
                    
                    # 标记错误
                    self._mark_api_error(api_index, error_msg)
                    
                    # 检查是否是配额错误
                    if any(keyword in error_msg for keyword in ["quota", "limit", "permission", "resource_exhausted"]):
                        print(f"   → 配额/权限问题，切换API")
                        # 立即尝试下一个API
                        continue
                    else:
                        # 其他错误，等待后重试
                        if retry < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2
                            continue
                        else:
                            raise e
        
        raise Exception(f"API调用失败，已重试 {max_retries} 次")
    
    def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        with self.index_lock:
            if key_index < len(self.api_configs):
                self.usage_counts[key_index] += 1
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.index_lock:
            total_calls = sum(self.api_call_counts.values())
            total_success = sum(self.success_counts.values())
            success_rate = (total_success / total_calls * 100) if total_calls > 0 else 0
            
            return {
                "total_calls": total_calls,
                "total_success": total_success,
                "success_rate": f"{success_rate:.1f}%",
                "api_details": [
                    {
                        "name": config["name"],
                        "calls": self.api_call_counts[i],
                        "success": self.success_counts[i],
                        "errors": config["error_count"],
                        "available": config["is_available"]
                    }
                    for i, config in enumerate(self.api_configs)
                ]
            }

class OptimizedAsyncBatchProcessor:
    """优化的异步批处理器"""
    
    def __init__(self, max_concurrent: int = 10):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_batch_with_retry(self, tasks: List, batch_size: int = None):
        """带重试的批处理"""
        if batch_size is None:
            batch_size = self.max_concurrent
        
        results = []
        failed_tasks = []
        
        # 分批处理
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i+batch_size]
            print(f"🔄 处理批次 {i//batch_size + 1}/{(len(tasks)-1)//batch_size + 1} ({len(batch)} 个任务)")
            
            try:
                # 并行执行批次
                batch_results = await asyncio.gather(*batch, return_exceptions=True)
                
                # 处理结果
                for j, result in enumerate(batch_results):
                    if isinstance(result, Exception):
                        print(f"   ❌ 任务 {i+j+1} 失败: {str(result)}")
                        failed_tasks.append(tasks[i+j])
                    else:
                        results.append(result)
                
                print(f"   ✅ 批次完成: {len(batch) - len([r for r in batch_results if isinstance(r, Exception)])} 成功")
                
            except Exception as e:
                print(f"   ❌ 批次失败: {str(e)}")
                failed_tasks.extend(batch)
        
        # 重试失败的任务
        if failed_tasks:
            print(f"🔄 重试 {len(failed_tasks)} 个失败任务...")
            retry_results = await self._retry_failed_tasks(failed_tasks)
            results.extend(retry_results)
        
        return results
    
    async def _retry_failed_tasks(self, failed_tasks: List, max_retries: int = 2):
        """重试失败的任务"""
        results = []
        
        for retry in range(max_retries):
            if not failed_tasks:
                break
            
            print(f"   重试第 {retry + 1} 轮...")
            retry_results = await asyncio.gather(*failed_tasks, return_exceptions=True)
            
            new_failed_tasks = []
            for i, result in enumerate(retry_results):
                if isinstance(result, Exception):
                    new_failed_tasks.append(failed_tasks[i])
                else:
                    results.append(result)
            
            failed_tasks = new_failed_tasks
            
            if failed_tasks and retry < max_retries - 1:
                await asyncio.sleep(2 ** retry)  # 指数退避
        
        if failed_tasks:
            print(f"   ⚠️ {len(failed_tasks)} 个任务最终失败")
        
        return results

def create_optimized_async_manager(api_keys: List[str], model_names: List[str]) -> OptimizedAsyncGeminiAPIManager:
    """创建优化的异步API管理器"""
    return OptimizedAsyncGeminiAPIManager(api_keys, model_names)

def create_batch_processor(max_concurrent: int = 10) -> OptimizedAsyncBatchProcessor:
    """创建批处理器"""
    return OptimizedAsyncBatchProcessor(max_concurrent)

# 测试函数
async def test_optimized_async_manager():
    """测试优化的异步管理器"""
    print("🧪 测试优化的异步API管理器")
    
    # 模拟API密钥
    test_api_keys = ["test_key_1", "test_key_2", "test_key_3"]
    test_models = ["gemini-2.5-pro", "gemini-2.5-flash"]
    
    try:
        manager = OptimizedAsyncGeminiAPIManager(test_api_keys, test_models)
        
        # 测试API配置获取
        config = manager._get_next_available_api()
        if config:
            print(f"✅ 获取API配置成功: {config['api_name']}")
        else:
            print("❌ 无法获取API配置")
        
        # 测试统计信息
        stats = manager.get_statistics()
        print(f"📊 统计信息: {stats}")
        
        print("✅ 优化的异步管理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    asyncio.run(test_optimized_async_manager())
