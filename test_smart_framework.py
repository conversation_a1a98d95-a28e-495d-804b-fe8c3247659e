#!/usr/bin/env python3
"""
测试智能框架系统
验证最多10个一级标题，每个最多10个子节点的智能框架功能
"""

import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_smart_framework_template():
    """测试智能框架模板"""
    print("🧪 测试智能框架模板")
    print("=" * 60)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 获取智能框架模板
    template = generator._get_smart_framework_template()
    
    print("📋 智能框架模板结构:")
    sections = template.get("sections", [])
    print(f"   • 总一级标题数: {len(sections)} (最大10个)")
    
    for i, section in enumerate(sections, 1):
        title = section.get("title", "")
        active = section.get("active", False)
        children = section.get("children", [])
        active_children = [child for child in children if child.get("active", False)]
        
        status = "✅ 活跃" if active else "⚪ 预留"
        print(f"   {i}. {title} - {status}")
        print(f"      └─ 子节点: {len(children)} 个 (活跃: {len(active_children)} 个)")

def test_ai_framework_matching():
    """测试AI框架匹配功能"""
    print("\n🧪 测试AI框架匹配功能")
    print("=" * 60)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 模拟AI生成的框架
    ai_framework = {
        "sections": [
            {
                "title": "1. 核电产业概览与发展现状",
                "level": 1,
                "children": [
                    {"title": "1.1. 核电技术基础", "level": 2, "children": []},
                    {"title": "1.2. 全球核电发展历程", "level": 2, "children": []},
                    {"title": "1.3. 核电安全与监管", "level": 2, "children": []},
                    {"title": "1.4. 核电经济性分析", "level": 2, "children": []}
                ]
            },
            {
                "title": "2. 核反应堆技术深度解析",
                "level": 1,
                "children": [
                    {"title": "2.1. 压水堆技术", "level": 2, "children": []},
                    {"title": "2.2. 沸水堆技术", "level": 2, "children": []},
                    {"title": "2.3. 先进反应堆技术", "level": 2, "children": []},
                    {"title": "2.4. 小型模块化反应堆", "level": 2, "children": []},
                    {"title": "2.5. 第四代反应堆", "level": 2, "children": []}
                ]
            },
            {
                "title": "3. 全球核电市场分析",
                "level": 1,
                "children": [
                    {"title": "3.1. 市场规模与增长", "level": 2, "children": []},
                    {"title": "3.2. 主要国家核电政策", "level": 2, "children": []},
                    {"title": "3.3. 核电投资趋势", "level": 2, "children": []}
                ]
            }
        ]
    }
    
    topic = "核电产业研究报告"
    
    print(f"📋 模拟AI生成的框架 (主题: {topic}):")
    for section in ai_framework["sections"]:
        print(f"   • {section['title']}")
        for child in section.get("children", []):
            print(f"     └─ {child['title']}")
    
    print("\n🔄 使用智能框架系统处理...")
    smart_framework = generator.create_smart_framework_from_ai_response(ai_framework, topic)
    
    print("\n✅ 智能框架处理完成！")
    return smart_framework

def test_framework_statistics():
    """测试框架统计功能"""
    print("\n🧪 测试框架统计功能")
    print("=" * 60)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 创建一个测试框架
    test_framework = {
        "sections": [
            {
                "title": "1. 测试一级标题1",
                "level": 1,
                "children": [
                    {"title": "1.1. 测试二级标题1", "level": 2, "children": []},
                    {"title": "1.2. 测试二级标题2", "level": 2, "children": []}
                ]
            },
            {
                "title": "2. 测试一级标题2",
                "level": 1,
                "children": [
                    {"title": "2.1. 测试二级标题1", "level": 2, "children": []},
                    {"title": "2.2. 测试二级标题2", "level": 2, "children": []},
                    {"title": "2.3. 测试二级标题3", "level": 2, "children": []}
                ]
            }
        ]
    }
    
    print("📊 测试框架统计报告:")
    generator._report_framework_statistics(test_framework)

def test_node_hiding():
    """测试节点隐藏功能"""
    print("\n🧪 测试节点隐藏功能")
    print("=" * 60)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 获取模板并设置一些节点为活跃
    template = generator._get_smart_framework_template()
    
    # 只激活前3个一级标题
    for i in range(3):
        if i < len(template["sections"]):
            template["sections"][i]["active"] = True
    
    # 其余设为非活跃
    for i in range(3, len(template["sections"])):
        template["sections"][i]["active"] = False
    
    print("🔧 隐藏未使用的节点...")
    cleaned_framework = generator._hide_unused_nodes(template)
    
    print("📊 清理后的框架:")
    sections = cleaned_framework.get("sections", [])
    print(f"   • 保留的一级标题: {len(sections)} 个")
    
    for i, section in enumerate(sections, 1):
        title = section.get("title", "")
        children = section.get("children", [])
        print(f"   {i}. {title}")
        print(f"      └─ 子节点: {len(children)} 个")

def main():
    """主测试函数"""
    print("🧠 智能框架系统测试")
    print("=" * 80)
    print("🎯 功能: 最多10个一级标题，每个最多10个子节点")
    print("🔧 特性: 智能匹配AI生成标题，自动隐藏未使用节点")
    print("=" * 80)
    
    try:
        # 测试1: 智能框架模板
        test_smart_framework_template()
        
        # 测试2: AI框架匹配
        smart_framework = test_ai_framework_matching()
        
        # 测试3: 框架统计
        test_framework_statistics()
        
        # 测试4: 节点隐藏
        test_node_hiding()
        
        print("\n" + "=" * 80)
        print("✅ 智能框架系统测试完成！")
        print("💡 系统已准备好处理AI生成的框架并进行智能优化")
        print("🎯 支持最多10个一级标题，每个最多10个子节点")
        print("🔧 自动隐藏未使用的节点，优化框架结构")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
